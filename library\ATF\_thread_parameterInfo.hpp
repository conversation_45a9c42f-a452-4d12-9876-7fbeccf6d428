// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_thread_parameter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _thread_parameterEndThread2_ptr = void (WINAPIV*)(struct _thread_parameter*);
        using _thread_parameterEndThread2_clbk = void (WINAPIV*)(struct _thread_parameter*, _thread_parameterEndThread2_ptr);
        using _thread_parameterSetParameter4_ptr = void (WINAPIV*)(struct _thread_parameter*, int, void*, int);
        using _thread_parameterSetParameter4_clbk = void (WINAPIV*)(struct _thread_parameter*, int, void*, int, _thread_parameterSetParameter4_ptr);
        
        using _thread_parameterctor__thread_parameter6_ptr = void (WINAPIV*)(struct _thread_parameter*);
        using _thread_parameterctor__thread_parameter6_clbk = void (WINAPIV*)(struct _thread_parameter*, _thread_parameterctor__thread_parameter6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

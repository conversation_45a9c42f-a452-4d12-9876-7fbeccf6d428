// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PMD.hpp>
#include <_TypeDescriptor.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _s__CatchableType
    {
        unsigned int properties;
        _TypeDescriptor *pType;
        _PMD _thisDisplacement;
        int sizeOrOffset;
        void (WINAPIV *copyFunction)();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_talk_crystal_matrial_combine_node.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _talk_crystal_matrial_combine_nodeConsume2_ptr = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, int);
        using _talk_crystal_matrial_combine_nodeConsume2_clbk = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, int, _talk_crystal_matrial_combine_nodeConsume2_ptr);
        using _talk_crystal_matrial_combine_nodeGetMixNeedNum4_ptr = int (WINAPIV*)(struct _talk_crystal_matrial_combine_node*);
        using _talk_crystal_matrial_combine_nodeGetMixNeedNum4_clbk = int (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, _talk_crystal_matrial_combine_nodeGetMixNeedNum4_ptr);
        using _talk_crystal_matrial_combine_nodeGetRequiredSlotCount6_ptr = int (WINAPIV*)(struct _talk_crystal_matrial_combine_node*);
        using _talk_crystal_matrial_combine_nodeGetRequiredSlotCount6_clbk = int (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, _talk_crystal_matrial_combine_nodeGetRequiredSlotCount6_ptr);
        using _talk_crystal_matrial_combine_nodeInit8_ptr = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*);
        using _talk_crystal_matrial_combine_nodeInit8_clbk = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, _talk_crystal_matrial_combine_nodeInit8_ptr);
        using _talk_crystal_matrial_combine_nodeMake10_ptr = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, int);
        using _talk_crystal_matrial_combine_nodeMake10_clbk = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, int, _talk_crystal_matrial_combine_nodeMake10_ptr);
        using _talk_crystal_matrial_combine_nodePush12_ptr = bool (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, _STORAGE_LIST::_db_con*, char, char);
        using _talk_crystal_matrial_combine_nodePush12_clbk = bool (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, _STORAGE_LIST::_db_con*, char, char, _talk_crystal_matrial_combine_nodePush12_ptr);
        using _talk_crystal_matrial_combine_nodeSet14_ptr = bool (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, int, int, char, uint16_t);
        using _talk_crystal_matrial_combine_nodeSet14_clbk = bool (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, int, int, char, uint16_t, _talk_crystal_matrial_combine_nodeSet14_ptr);
        
        using _talk_crystal_matrial_combine_nodector__talk_crystal_matrial_combine_node16_ptr = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*);
        using _talk_crystal_matrial_combine_nodector__talk_crystal_matrial_combine_node16_clbk = void (WINAPIV*)(struct _talk_crystal_matrial_combine_node*, _talk_crystal_matrial_combine_nodector__talk_crystal_matrial_combine_node16_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

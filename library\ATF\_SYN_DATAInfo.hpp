// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYN_DATA.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _SYN_DATActor__SYN_DATA2_ptr = void (WINAPIV*)(struct _SYN_DATA*);
        using _SYN_DATActor__SYN_DATA2_clbk = void (WINAPIV*)(struct _SYN_DATA*, _SYN_DATActor__SYN_DATA2_ptr);
        
        using _SYN_DATAdtor__SYN_DATA6_ptr = void (WINAPIV*)(struct _SYN_DATA*);
        using _SYN_DATAdtor__SYN_DATA6_clbk = void (WINAPIV*)(struct _SYN_DATA*, _SYN_DATAdtor__SYN_DATA6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _SYNC_STATE
    {
        bool bEnter;
        bool bReged;
        bool bSelect;
    public:
        void Init();
        _SYNC_STATE();
        void ctor__SYNC_STATE();
        bool chk_enter();
        bool chk_reged();
        bool chk_select();
        void re_lobby();
    };
END_ATF_NAMESPACE

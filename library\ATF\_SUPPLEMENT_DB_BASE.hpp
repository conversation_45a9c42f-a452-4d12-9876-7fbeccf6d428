// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _SUPPLEMENT_DB_BASE
    {
        long double dPvpPointLeak;
        bool bLastAttBuff;
        unsigned int dwBufPotionEndTime;
        unsigned int dwRaceBuffClear;
        char byVoted;
        char VoteEnable;
        unsigned __int16 wScanerCnt;
        unsigned int dwScanerGetDate;
        unsigned int dwAccumPlayTime;
        unsigned int dwLastResetDate;
        unsigned int dwActionPoint[3];
    public:
        void Init();
        _SUPPLEMENT_DB_BASE();
        void ctor__SUPPLEMENT_DB_BASE();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_SUPPLEMENT_DB_BASE, 45>(), "_SUPPLEMENT_DB_BASE");
END_ATF_NAMESPACE

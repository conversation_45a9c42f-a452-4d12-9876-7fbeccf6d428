// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_LIST.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct _talk_crystal_matrial_combine_node
    {
        struct _matrialinfo
        {
            _STORAGE_LIST::_db_con *m_pMatrial;
            char m_byConsume;
            char m_byUseCount;
            char m_byClientIndex;
        public:
            void Init();
            _matrialinfo();
            void ctor__matrialinfo();
        };
        int m_nMixIndex;
        char m_byTableCode;
        unsigned __int16 m_wItemIndex;
        int m_nNeedItemNum;
        int m_nRequiredSlotCount;
        _matrialinfo m_matrialList[24];
        int m_nMatrialCount;
        int m_nMatrialOverlapCount;
        int m_nMakeCount;
        bool m_bUse;
         _STORAGE_LIST::_db_con m_MakeItem;
    public:
        void Consume(int nConsumeCount);
        int GetMixNeedNum();
        int GetRequiredSlotCount();
        void Init();
        void Make(int nMakeCount);
        bool Push(_STORAGE_LIST::_db_con* pItem, char byUseCount, char byClientIndex);
        bool Set(int nMixIndex, int nNeedItemNum, char byTableCode, uint16_t wItemIndex);
        _talk_crystal_matrial_combine_node();
        void ctor__talk_crystal_matrial_combine_node();
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _TI_FINDCHILDREN_PARAMS
    {
        unsigned int Count;
        unsigned int Start;
        unsigned int ChildId[1];
    };    
    static_assert(ATF::checkSize<_TI_FINDCHILDREN_PARAMS, 12>(), "_TI_FINDCHILDREN_PARAMS");
END_ATF_NAMESPACE

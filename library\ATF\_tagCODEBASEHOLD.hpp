// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _tagCODEBASEHOLD
    {
        unsigned int cbSize;
        wchar_t *szDistUnit;
        wchar_t *szCodeBase;
        unsigned int dwVersionMS;
        unsigned int dwVersionLS;
        unsigned int dwStyle;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>



START_ATF_NAMESPACE
    struct _TIME_ZONE_INFORMATION
    {
        int Bias;
        wchar_t StandardName[32];
        _SYSTEMTIME StandardDate;
        int StandardBias;
        wchar_t DaylightName[32];
        _SYSTEMTIME DaylightDate;
        int DaylightBias;
    };
END_ATF_NAMESPACE

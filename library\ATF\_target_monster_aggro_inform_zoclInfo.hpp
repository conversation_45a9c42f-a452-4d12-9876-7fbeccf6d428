// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_target_monster_aggro_inform_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _target_monster_aggro_inform_zoclInit2_ptr = void (WINAPIV*)(struct _target_monster_aggro_inform_zocl*);
        using _target_monster_aggro_inform_zoclInit2_clbk = void (WINAPIV*)(struct _target_monster_aggro_inform_zocl*, _target_monster_aggro_inform_zoclInit2_ptr);
        
        using _target_monster_aggro_inform_zoclctor__target_monster_aggro_inform_zocl4_ptr = void (WINAPIV*)(struct _target_monster_aggro_inform_zocl*);
        using _target_monster_aggro_inform_zoclctor__target_monster_aggro_inform_zocl4_clbk = void (WINAPIV*)(struct _target_monster_aggro_inform_zocl*, _target_monster_aggro_inform_zoclctor__target_monster_aggro_inform_zocl4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

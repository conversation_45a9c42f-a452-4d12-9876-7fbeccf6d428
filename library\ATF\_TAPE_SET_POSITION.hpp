// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _TAPE_SET_POSITION
    {
        unsigned int Method;
        unsigned int Partition;
        _LARGE_INTEGER Offset;
        char Immediate;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

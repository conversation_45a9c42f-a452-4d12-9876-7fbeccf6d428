// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _talik_recvr_list
    {
        struct __talik_recvr
        {
            char byTableCode;
            struct _base_fld *m_pFld;
            int nRecvrPoint;
        };
        char byTalikNum;
        __talik_recvr TalikInfo[14];
    public:
        _talik_recvr_list();
        void ctor__talik_recvr_list();
        int size();
    };
END_ATF_NAMESPACE

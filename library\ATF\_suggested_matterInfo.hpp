// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_suggested_matter.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _suggested_matterClear2_ptr = void (WINAPIV*)(struct _suggested_matter*);
        using _suggested_matterClear2_clbk = void (WINAPIV*)(struct _suggested_matter*, _suggested_matterClear2_ptr);
        using _suggested_matterIsVotable4_ptr = bool (WINAPIV*)(struct _suggested_matter*, unsigned int);
        using _suggested_matterIsVotable4_clbk = bool (WINAPIV*)(struct _suggested_matter*, unsigned int, _suggested_matterIsVotable4_ptr);
        
        using _suggested_matterctor__suggested_matter6_ptr = void (WINAPIV*)(struct _suggested_matter*);
        using _suggested_matterctor__suggested_matter6_clbk = void (WINAPIV*)(struct _suggested_matter*, _suggested_matterctor__suggested_matter6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _SYSTEMTIME
    {
        unsigned __int16 wYear;
        unsigned __int16 wMonth;
        unsigned __int16 wDayOfWeek;
        unsigned __int16 wDay;
        unsigned __int16 wHour;
        unsigned __int16 wMinute;
        unsigned __int16 wSecond;
        unsigned __int16 wMilliseconds;
    };    
    static_assert(ATF::checkSize<_SYSTEMTIME, 16>(), "_SYSTEMTIME");
END_ATF_NAMESPACE

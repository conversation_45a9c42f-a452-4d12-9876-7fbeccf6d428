// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYN_HEADER.hpp>


START_ATF_NAMESPACE
    struct  _SYN_DATA : _SYN_HEADER
    {
        bool m_bUse;
        bool m_bProcess;
        char *m_psData;
    public:
        _SYN_DATA();
        void ctor__SYN_DATA();
        ~_SYN_DATA();
        void dtor__SYN_DATA();
    };
END_ATF_NAMESPACE

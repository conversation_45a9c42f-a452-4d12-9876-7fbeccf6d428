// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_THREAD_CONFIG.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _THREAD_CONFIGctor__THREAD_CONFIG2_ptr = void (WIN<PERSON>IV*)(struct _THREAD_CONFIG*);
        using _THREAD_CONFIGctor__THREAD_CONFIG2_clbk = void (WINAPIV*)(struct _THREAD_CONFIG*, _THREAD_CONFIGctor__THREAD_CONFIG2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

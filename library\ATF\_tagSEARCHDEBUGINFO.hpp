// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _tagSEARCHDEBUGINFO
    {
        unsigned int cb;
        int fMainDebugFile;
        char *szMod;
        char *szLib;
        char *szObj;
        char **rgszTriedThese;
        char szValidatedFile[260];
        int (WINAPIV *pfnValidateDebugInfoFile)(const char *, unsigned int *);
        char *szExe;
    };
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_suggested_matter_change_taxrate.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _suggested_matter_change_taxratector__suggested_matter_change_taxrate2_ptr = void (WINAPIV*)(struct _suggested_matter_change_taxrate*);
        using _suggested_matter_change_taxratector__suggested_matter_change_taxrate2_clbk = void (WINAPIV*)(struct _suggested_matter_change_taxrate*, _suggested_matter_change_taxratector__suggested_matter_change_taxrate2_ptr);
        using _suggested_matter_change_taxrateinit4_ptr = void (WINAPIV*)(struct _suggested_matter_change_taxrate*);
        using _suggested_matter_change_taxrateinit4_clbk = void (WINAPIV*)(struct _suggested_matter_change_taxrate*, _suggested_matter_change_taxrateinit4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_talik_recvr_list.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _talik_recvr_listctor__talik_recvr_list2_ptr = void (WINAPIV*)(struct _talik_recvr_list*);
        using _talik_recvr_listctor__talik_recvr_list2_clbk = void (WINAPIV*)(struct _talik_recvr_list*, _talik_recvr_listctor__talik_recvr_list2_ptr);
        using _talik_recvr_listsize4_ptr = int (WINAPIV*)(struct _talik_recvr_list*);
        using _talik_recvr_listsize4_clbk = int (WINAPIV*)(struct _talik_recvr_list*, _talik_recvr_listsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_talik_crystal_exchange_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _talik_crystal_exchange_zoclctor__talik_crystal_exchange_zocl2_ptr = void (WINAPIV*)(struct _talik_crystal_exchange_zocl*);
        using _talik_crystal_exchange_zoclctor__talik_crystal_exchange_zocl2_clbk = void (WINAPIV*)(struct _talik_crystal_exchange_zocl*, _talik_crystal_exchange_zoclctor__talik_crystal_exchange_zocl2_ptr);
        using _talik_crystal_exchange_zoclsize4_ptr = int (WINAPIV*)(struct _talik_crystal_exchange_zocl*);
        using _talik_crystal_exchange_zoclsize4_clbk = int (WINAPIV*)(struct _talik_crystal_exchange_zocl*, _talik_crystal_exchange_zoclsize4_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _target_player_damage_contsf_allinform_zocl
    {
        struct  _playercontsf
        {
            unsigned __int16 wSfcode;
            char byContCount;
        };
        unsigned int dwSerial;
        char byContCount;
        _playercontsf m_PlayerContSf[8];
    public:
        void Init();
        static bool IsSame(struct _target_player_damage_contsf_allinform_zocl* src1, struct _target_player_damage_contsf_allinform_zocl* src2);
        _target_player_damage_contsf_allinform_zocl();
        void ctor__target_player_damage_contsf_allinform_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_target_player_damage_contsf_allinform_zocl, 29>(), "_target_player_damage_contsf_allinform_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <IInternetProtocol.hpp>
#include <IInternetProtocolSink.hpp>
#include <IUnknown.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _tagPROTOCOLFILTERDATA
    {
        unsigned int cbSize;
        IInternetProtocolSink *pProtocolSink;
        IInternetProtocol *pProtocol;
        IUnknown *pUnk;
        unsigned int dwFilterFlags;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

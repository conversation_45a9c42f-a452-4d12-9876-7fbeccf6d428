// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <HWND__.hpp>
#include <IShellBrowser.hpp>
#include <IShellView.hpp>
#include <_GUID.hpp>
#include <__MIDL___MIDL_itf_shobjidl_0202_0003.hpp>
#include <tagRECT.hpp>


START_ATF_NAMESPACE
    struct _SV2CVW2_PARAMS
    {
        unsigned int cbSize;
        IShellView *psvPrev;
        __MIDL___MIDL_itf_shobjidl_0202_0003 *pfs;
        IShellBrowser *psbOwner;
        tagRECT *prcView;
        _GUID *pvid;
        HWND__ *hwndView;
    };
END_ATF_NAMESPACE

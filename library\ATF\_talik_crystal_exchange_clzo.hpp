// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _talik_crystal_exchange_clzo
    {
        struct  _list
        {
            unsigned __int16 wSerial;
            char byItemCount;
        };
        char byExchangeNum;
        _list Item[24];
    };
END_ATF_NAMESPACE

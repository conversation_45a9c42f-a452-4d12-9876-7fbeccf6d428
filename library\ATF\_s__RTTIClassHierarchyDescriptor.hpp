// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_s__RTTIBaseClassArray.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    const struct  _s__RTTIClassHierarchyDescriptor
    {
        unsigned int signature;
        unsigned int attributes;
        unsigned int numBaseClasses;
        _s__RTTIBaseClassArray *pBaseClassArray;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_s__CatchableTypeArray.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _s__ThrowInfo
    {
        unsigned int attributes;
        void (WINAPIV *pmfnUnwind)();
        int (*pForwardCompat)(...);
        _s__CatchableTypeArray *pCatchableTypeArray;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

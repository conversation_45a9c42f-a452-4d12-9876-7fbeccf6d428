// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CHRID.hpp>
#include <_throw_skill_result_one_zocl.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _throw_skill_result_other_zocl
    {
        char byRetCode;
        _CHRID idPerformer;
        char bySkillIndex;
        char byAttackSerial;
        _CHRID idDster;
        char byEffectedNum;
        _throw_skill_result_one_zocl::__effected_list list[30];
    public:
        _throw_skill_result_other_zocl();
        void ctor__throw_skill_result_other_zocl();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_throw_skill_result_other_zocl, 258>(), "_throw_skill_result_other_zocl");
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _thread_parameter
    {
        int m_bStart;
        void *m_pParam;
        int m_nIndex;
    public:
        void EndThread();
        void SetParameter(int bStart, void* pParam, int nIndex);
        _thread_parameter();
        void ctor__thread_parameter();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_thread_parameter, 24>(), "_thread_parameter");
END_ATF_NAMESPACE

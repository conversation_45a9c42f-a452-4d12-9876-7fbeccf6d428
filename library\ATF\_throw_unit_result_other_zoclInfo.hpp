// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_throw_unit_result_other_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _throw_unit_result_other_zoclctor__throw_unit_result_other_zocl2_ptr = void (WINAPIV*)(struct _throw_unit_result_other_zocl*);
        using _throw_unit_result_other_zoclctor__throw_unit_result_other_zocl2_clbk = void (WINAPIV*)(struct _throw_unit_result_other_zocl*, _throw_unit_result_other_zoclctor__throw_unit_result_other_zocl2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

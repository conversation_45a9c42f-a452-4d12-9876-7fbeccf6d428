// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$6876E22C924B2B44D9A7B559FA60FF60.hpp>


START_ATF_NAMESPACE
    struct _SYSTEM_LOGICAL_PROCESSOR_INFORMATION
    {
        unsigned __int64 ProcessorMask;
        _LOGICAL_PROCESSOR_RELATIONSHIP Relationship;
        $6876E22C924B2B44D9A7B559FA60FF60 ___u2;
    };
END_ATF_NAMESPACE

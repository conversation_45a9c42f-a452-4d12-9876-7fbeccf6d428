// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_KDHELP64.hpp>
#include <_tagADDRESS64.hpp>


START_ATF_NAMESPACE
    struct _tagSTACKFRAME64
    {
        _tagADDRESS64 AddrPC;
        _tagADDRESS64 AddrReturn;
        _tagADDRESS64 AddrFrame;
        _tagADDRESS64 AddrStack;
        _tagADDRESS64 AddrBStore;
        void *FuncTableEntry;
        unsigned __int64 Params[4];
        int Far;
        int Virtual;
        unsigned __int64 Reserved[3];
        _KDHELP64 KdHelp;
    };
END_ATF_NAMESPACE

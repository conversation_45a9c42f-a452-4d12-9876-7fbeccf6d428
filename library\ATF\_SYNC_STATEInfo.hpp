// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYNC_STATE.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _SYNC_STATEInit2_ptr = void (WINAPIV*)(struct _SYNC_STATE*);
        using _SYNC_STATEInit2_clbk = void (WINAPIV*)(struct _SYNC_STATE*, _SYNC_STATEInit2_ptr);
        
        using _SYNC_STATEctor__SYNC_STATE4_ptr = void (WINAPIV*)(struct _SYNC_STATE*);
        using _SYNC_STATEctor__SYNC_STATE4_clbk = void (WINAPIV*)(struct _SYNC_STATE*, _SYNC_STATEctor__SYNC_STATE4_ptr);
        using _SYNC_STATEchk_enter6_ptr = bool (WINAPIV*)(struct _SYNC_STATE*);
        using _SYNC_STATEchk_enter6_clbk = bool (WINAPIV*)(struct _SYNC_STATE*, _SYNC_STATEchk_enter6_ptr);
        using _SYNC_STATEchk_reged8_ptr = bool (WINAPIV*)(struct _SYNC_STATE*);
        using _SYNC_STATEchk_reged8_clbk = bool (WINAPIV*)(struct _SYNC_STATE*, _SYNC_STATEchk_reged8_ptr);
        using _SYNC_STATEchk_select10_ptr = bool (WINAPIV*)(struct _SYNC_STATE*);
        using _SYNC_STATEchk_select10_clbk = bool (WINAPIV*)(struct _SYNC_STATE*, _SYNC_STATEchk_select10_ptr);
        using _SYNC_STATEre_lobby12_ptr = void (WINAPIV*)(struct _SYNC_STATE*);
        using _SYNC_STATEre_lobby12_clbk = void (WINAPIV*)(struct _SYNC_STATE*, _SYNC_STATEre_lobby12_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

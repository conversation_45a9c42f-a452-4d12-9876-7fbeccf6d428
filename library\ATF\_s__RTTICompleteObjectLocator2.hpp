// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_TypeDescriptor.hpp>
#include <_s__RTTIClassHierarchyDescriptor.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _s__RTTICompleteObjectLocator2
    {
        unsigned int signature;
        unsigned int offset;
        unsigned int cdOffset;
        _TypeDescriptor *pTypeDescriptor;
        _s__RTTIClassHierarchyDescriptor *pClassDescriptor;
        _s__RTTICompleteObjectLocator2 *pSelf;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$4337E7006B9D39B44FCEE9C2D2A7EC76.hpp>


START_ATF_NAMESPACE
    struct _SYSTEM_INFO
    {
        $4337E7006B9D39B44FCEE9C2D2A7EC76 ___u0;
        unsigned int dwPageSize;
        void *lpMinimumApplicationAddress;
        void *lpMaximumApplicationAddress;
        unsigned __int64 dwActiveProcessorMask;
        unsigned int dwNumberOfProcessors;
        unsigned int dwProcessorType;
        unsigned int dwAllocationGranularity;
        unsigned __int16 wProcessorLevel;
        unsigned __int16 wProcessorRevision;
    };    
    static_assert(ATF::checkSize<_SYSTEM_INFO, 48>(), "_SYSTEM_INFO");
END_ATF_NAMESPACE

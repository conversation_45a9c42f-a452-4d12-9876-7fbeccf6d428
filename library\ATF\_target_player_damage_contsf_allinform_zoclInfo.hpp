// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_target_player_damage_contsf_allinform_zocl.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _target_player_damage_contsf_allinform_zoclInit2_ptr = void (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*);
        using _target_player_damage_contsf_allinform_zoclInit2_clbk = void (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*, _target_player_damage_contsf_allinform_zoclInit2_ptr);
        using _target_player_damage_contsf_allinform_zoclIsSame4_ptr = bool (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*, struct _target_player_damage_contsf_allinform_zocl*);
        using _target_player_damage_contsf_allinform_zoclIsSame4_clbk = bool (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*, struct _target_player_damage_contsf_allinform_zocl*, _target_player_damage_contsf_allinform_zoclIsSame4_ptr);
        
        using _target_player_damage_contsf_allinform_zoclctor__target_player_damage_contsf_allinform_zocl6_ptr = void (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*);
        using _target_player_damage_contsf_allinform_zoclctor__target_player_damage_contsf_allinform_zocl6_clbk = void (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*, _target_player_damage_contsf_allinform_zoclctor__target_player_damage_contsf_allinform_zocl6_ptr);
        using _target_player_damage_contsf_allinform_zoclsize8_ptr = int (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*);
        using _target_player_damage_contsf_allinform_zoclsize8_clbk = int (WINAPIV*)(struct _target_player_damage_contsf_allinform_zocl*, _target_player_damage_contsf_allinform_zoclsize8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

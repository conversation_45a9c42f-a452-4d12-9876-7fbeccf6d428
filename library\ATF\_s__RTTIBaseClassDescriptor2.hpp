// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_PMD.hpp>
#include <_TypeDescriptor.hpp>
#include <_s__RTTIClassHierarchyDescriptor.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct  _s__RTTIBaseClassDescriptor2
    {
        _TypeDescriptor *pTypeDescriptor;
        unsigned int numContainedBases;
        _PMD where;
        unsigned int attributes;
        _s__RTTIClassHierarchyDescriptor *pClassDescriptor;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE

// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYSTEMTIME.hpp>


START_ATF_NAMESPACE
    struct _tagHIT_LOGGING_INFO
    {
        unsigned int dwStructSize;
        char *lpszLoggedUrlName;
        _SYSTEMTIME StartTime;
        _SYSTEMTIME EndTime;
        char *lpszExtendedInfo;
    };
END_ATF_NAMESPACE

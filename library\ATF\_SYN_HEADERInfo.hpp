// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_SYN_HEADER.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using _SYN_HEADERctor__SYN_HEADER2_ptr = void (WINAPIV*)(struct _SYN_HEADER*);
        using _SYN_HEADERctor__SYN_HEADER2_clbk = void (WINAPIV*)(struct _SYN_HEADER*, _SYN_HEADERctor__SYN_HEADER2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE

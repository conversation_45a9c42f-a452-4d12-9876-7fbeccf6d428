// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _suggested_matter
    {
        char byMatterType;
        unsigned int dwMatterDst;
        char wszMatterDst[64];
        unsigned int dwMatterObj1;
        unsigned int dwMatterObj2;
        unsigned int dwMatterObj3;
        char byVoteState[2];
        char wszComment[65];
        unsigned int dwMatterVoteSynKey;
        unsigned int dwStartTime;
        int nTotal_VotableMemNum;
        struct _guild_member_info *VotableMem[50];
    public:
        void Clear();
        bool IsVotable(unsigned int dwSerial);
        _suggested_matter();
        void ctor__suggested_matter();
    };
END_ATF_NAMESPACE

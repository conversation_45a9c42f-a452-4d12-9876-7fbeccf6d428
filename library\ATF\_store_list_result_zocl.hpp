// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _store_list_result_zocl
    {
        struct _store_pos
        {
            unsigned int dwStoreIndex;
            float fPos[3];
        };
        char by<PERSON>toreNum;
        _store_pos StorePos[100];
    public:
        _store_list_result_zocl();
        void ctor__store_list_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_store_list_result_zocl, 1601>(), "_store_list_result_zocl");
END_ATF_NAMESPACE

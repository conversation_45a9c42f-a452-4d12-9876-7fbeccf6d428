// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LUID.hpp>
#include <_LUID_AND_ATTRIBUTES.hpp>
#include <_SID_AND_ATTRIBUTES.hpp>


START_ATF_NAMESPACE
    struct _TOKEN_GROUPS_AND_PRIVILEGES
    {
        unsigned int SidCount;
        unsigned int SidLength;
        _SID_AND_ATTRIBUTES *Sids;
        unsigned int RestrictedSidCount;
        unsigned int RestrictedSidLength;
        _SID_AND_ATTRIBUTES *RestrictedSids;
        unsigned int PrivilegeCount;
        unsigned int PrivilegeLength;
        _LUID_AND_ATTRIBUTES *Privileges;
        _LUID AuthenticationId;
    };
END_ATF_NAMESPACE

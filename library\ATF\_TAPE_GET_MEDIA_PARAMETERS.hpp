// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _TAPE_GET_MEDIA_PARAMETERS
    {
        _LARGE_INTEGER Capacity;
        _LARGE_INTEGER Remaining;
        unsigned int BlockSize;
        unsigned int PartitionCount;
        char WriteProtected;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
